"""
Plugin System for AI Utility Orchestrator

Provides a flexible plugin architecture that allows users to extend the orchestrator
with custom components like LLM providers, tool loaders, and response formatters.
"""

import logging
import importlib
from typing import Dict, Any, List, Type, Protocol, runtime_checkable
from abc import ABC, abstractmethod

logger = logging.getLogger(__name__)


@runtime_checkable
class LLMProvider(Protocol):
    """Protocol for LLM providers."""
    
    def call(self, prompt: str, config: Dict[str, Any]) -> str:
        """Call the LLM with a prompt and return the response."""
        ...
    
    def supports_model(self, model_name: str) -> bool:
        """Check if this provider supports the given model."""
        ...


@runtime_checkable
class ToolLoader(Protocol):
    """Protocol for tool loaders."""
    
    def load_tools(self, config: Dict[str, Any]) -> List[Any]:
        """Load tools based on configuration."""
        ...
    
    def supports_source_type(self, source_type: str) -> bool:
        """Check if this loader supports the given source type."""
        ...


@runtime_checkable
class ResponseFormatter(Protocol):
    """Protocol for response formatters."""
    
    def format(self, raw_response: str, format_type: str) -> Any:
        """Format the raw response according to the format type."""
        ...
    
    def supports_format(self, format_type: str) -> bool:
        """Check if this formatter supports the given format type."""
        ...


@runtime_checkable
class ContextProvider(Protocol):
    """Protocol for context providers."""
    
    def get_context(self, user_id: str, config: Dict[str, Any]) -> Dict[str, Any]:
        """Get context for the given user."""
        ...
    
    def set_context(self, user_id: str, key: str, value: Any, config: Dict[str, Any]) -> None:
        """Set context for the given user."""
        ...


class PluginRegistry:
    """Registry for managing plugins."""
    
    def __init__(self):
        self.llm_providers: Dict[str, LLMProvider] = {}
        self.tool_loaders: Dict[str, ToolLoader] = {}
        self.response_formatters: Dict[str, ResponseFormatter] = {}
        self.context_providers: Dict[str, ContextProvider] = {}
        self.custom_plugins: Dict[str, Any] = {}
    
    def register_llm_provider(self, name: str, provider: LLMProvider) -> None:
        """Register an LLM provider."""
        if not isinstance(provider, LLMProvider):
            raise ValueError(f"Provider must implement LLMProvider protocol")
        self.llm_providers[name] = provider
        logger.info(f"Registered LLM provider: {name}")
    
    def register_tool_loader(self, name: str, loader: ToolLoader) -> None:
        """Register a tool loader."""
        if not isinstance(loader, ToolLoader):
            raise ValueError(f"Loader must implement ToolLoader protocol")
        self.tool_loaders[name] = loader
        logger.info(f"Registered tool loader: {name}")
    
    def register_response_formatter(self, name: str, formatter: ResponseFormatter) -> None:
        """Register a response formatter."""
        if not isinstance(formatter, ResponseFormatter):
            raise ValueError(f"Formatter must implement ResponseFormatter protocol")
        self.response_formatters[name] = formatter
        logger.info(f"Registered response formatter: {name}")
    
    def register_context_provider(self, name: str, provider: ContextProvider) -> None:
        """Register a context provider."""
        if not isinstance(provider, ContextProvider):
            raise ValueError(f"Provider must implement ContextProvider protocol")
        self.context_providers[name] = provider
        logger.info(f"Registered context provider: {name}")
    
    def register_custom_plugin(self, name: str, plugin: Any) -> None:
        """Register a custom plugin."""
        self.custom_plugins[name] = plugin
        logger.info(f"Registered custom plugin: {name}")
    
    def get_llm_provider(self, name: str) -> LLMProvider:
        """Get an LLM provider by name."""
        return self.llm_providers.get(name)
    
    def get_tool_loader(self, name: str) -> ToolLoader:
        """Get a tool loader by name."""
        return self.tool_loaders.get(name)
    
    def get_response_formatter(self, name: str) -> ResponseFormatter:
        """Get a response formatter by name."""
        return self.response_formatters.get(name)
    
    def get_context_provider(self, name: str) -> ContextProvider:
        """Get a context provider by name."""
        return self.context_providers.get(name)
    
    def get_custom_plugin(self, name: str) -> Any:
        """Get a custom plugin by name."""
        return self.custom_plugins.get(name)
    
    def find_llm_provider_for_model(self, model_name: str) -> LLMProvider:
        """Find an LLM provider that supports the given model."""
        for provider in self.llm_providers.values():
            if provider.supports_model(model_name):
                return provider
        return None
    
    def find_tool_loader_for_source(self, source_type: str) -> ToolLoader:
        """Find a tool loader that supports the given source type."""
        for loader in self.tool_loaders.values():
            if loader.supports_source_type(source_type):
                return loader
        return None
    
    def find_response_formatter_for_format(self, format_type: str) -> ResponseFormatter:
        """Find a response formatter that supports the given format."""
        for formatter in self.response_formatters.values():
            if formatter.supports_format(format_type):
                return formatter
        return None
    
    def list_plugins(self) -> Dict[str, List[str]]:
        """List all registered plugins."""
        return {
            "llm_providers": list(self.llm_providers.keys()),
            "tool_loaders": list(self.tool_loaders.keys()),
            "response_formatters": list(self.response_formatters.keys()),
            "context_providers": list(self.context_providers.keys()),
            "custom_plugins": list(self.custom_plugins.keys())
        }


class PluginLoader:
    """Loads plugins from configuration."""
    
    def __init__(self, registry: PluginRegistry):
        self.registry = registry
    
    def load_plugins_from_config(self, config: Dict[str, Any]) -> None:
        """Load plugins from configuration."""
        plugins_config = config.get("plugins", {})
        
        for plugin_type, plugins in plugins_config.items():
            if plugin_type == "llm_providers":
                self._load_llm_providers(plugins)
            elif plugin_type == "tool_loaders":
                self._load_tool_loaders(plugins)
            elif plugin_type == "response_formatters":
                self._load_response_formatters(plugins)
            elif plugin_type == "context_providers":
                self._load_context_providers(plugins)
            elif plugin_type == "custom":
                self._load_custom_plugins(plugins)
    
    def _load_llm_providers(self, providers: List[Dict[str, Any]]) -> None:
        """Load LLM providers from configuration."""
        for provider_config in providers:
            try:
                name = provider_config["name"]
                class_path = provider_config["class"]
                init_args = provider_config.get("init_args", {})
                
                provider_class = self._import_class(class_path)
                provider_instance = provider_class(**init_args)
                
                self.registry.register_llm_provider(name, provider_instance)
            except Exception as e:
                logger.error(f"Failed to load LLM provider: {e}")
    
    def _load_tool_loaders(self, loaders: List[Dict[str, Any]]) -> None:
        """Load tool loaders from configuration."""
        for loader_config in loaders:
            try:
                name = loader_config["name"]
                class_path = loader_config["class"]
                init_args = loader_config.get("init_args", {})
                
                loader_class = self._import_class(class_path)
                loader_instance = loader_class(**init_args)
                
                self.registry.register_tool_loader(name, loader_instance)
            except Exception as e:
                logger.error(f"Failed to load tool loader: {e}")
    
    def _load_response_formatters(self, formatters: List[Dict[str, Any]]) -> None:
        """Load response formatters from configuration."""
        for formatter_config in formatters:
            try:
                name = formatter_config["name"]
                class_path = formatter_config["class"]
                init_args = formatter_config.get("init_args", {})
                
                formatter_class = self._import_class(class_path)
                formatter_instance = formatter_class(**init_args)
                
                self.registry.register_response_formatter(name, formatter_instance)
            except Exception as e:
                logger.error(f"Failed to load response formatter: {e}")
    
    def _load_context_providers(self, providers: List[Dict[str, Any]]) -> None:
        """Load context providers from configuration."""
        for provider_config in providers:
            try:
                name = provider_config["name"]
                class_path = provider_config["class"]
                init_args = provider_config.get("init_args", {})
                
                provider_class = self._import_class(class_path)
                provider_instance = provider_class(**init_args)
                
                self.registry.register_context_provider(name, provider_instance)
            except Exception as e:
                logger.error(f"Failed to load context provider: {e}")
    
    def _load_custom_plugins(self, plugins: List[Dict[str, Any]]) -> None:
        """Load custom plugins from configuration."""
        for plugin_config in plugins:
            try:
                name = plugin_config["name"]
                class_path = plugin_config["class"]
                init_args = plugin_config.get("init_args", {})
                
                plugin_class = self._import_class(class_path)
                plugin_instance = plugin_class(**init_args)
                
                self.registry.register_custom_plugin(name, plugin_instance)
            except Exception as e:
                logger.error(f"Failed to load custom plugin: {e}")
    
    def _import_class(self, class_path: str) -> Type:
        """Import a class from a module path."""
        module_path, class_name = class_path.rsplit(".", 1)
        module = importlib.import_module(module_path)
        return getattr(module, class_name)


# Global plugin registry instance
_global_plugin_registry = PluginRegistry()


def get_plugin_registry() -> PluginRegistry:
    """Get the global plugin registry."""
    return _global_plugin_registry


def load_plugins_from_config(config: Dict[str, Any]) -> None:
    """Load plugins from configuration into the global registry."""
    loader = PluginLoader(_global_plugin_registry)
    loader.load_plugins_from_config(config)
