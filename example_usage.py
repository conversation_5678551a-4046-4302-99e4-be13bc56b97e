#!/usr/bin/env python3
"""
Minimal Dynamic Usage Example for AI Utility Orchestrator

This example demonstrates how to use the AI Utility Orchestrator in any project
with zero hardcoding - everything is provided dynamically by the user.
"""

import os
from ai_utility_orchestrator import agent_executor, ToolRegistry, Tool

# Example: User provides their own tools dynamically
def my_custom_calculator(params):
    """User's custom calculator tool."""
    expression = params.get("expression", "0")
    try:
        result = eval(expression.replace('^', '**'))
        return f"Calculation result: {expression} = {result}"
    except Exception as e:
        return f"Error: {str(e)}"

def my_text_processor(params):
    """User's custom text processing tool."""
    text = params.get("text", "")
    operation = params.get("operation", "uppercase")
    
    if operation == "uppercase":
        return text.upper()
    elif operation == "lowercase":
        return text.lower()
    elif operation == "reverse":
        return text[::-1]
    else:
        return f"Unknown operation: {operation}"

def my_data_formatter(params):
    """User's custom data formatter."""
    data = params.get("data", "")
    format_type = params.get("format", "json")
    
    if format_type == "json":
        import json
        try:
            parsed = json.loads(data)
            return json.dumps(parsed, indent=2)
        except:
            return f"Invalid JSON: {data}"
    elif format_type == "csv":
        return data.replace(",", " | ")
    else:
        return f"Formatted as {format_type}: {data}"

# Example usage function
def run_dynamic_example():
    """Demonstrate completely dynamic usage."""
    
    # User provides their own configuration - no hardcoded values
    user_config = {
        "llm": {
            "model": "gpt-4o-mini",  # User chooses their model
            "temperature": 0.7       # User sets their temperature
        },
        "system_prompt": "You are my personal AI assistant. Help me with calculations, text processing, and data formatting.",
        
        # User provides their own tools dynamically
        "tools": [
            {
                "name": "calculator",
                "description": "Performs mathematical calculations",
                "execute_func": "example_usage.my_custom_calculator",
                "schema": {
                    "type": "object",
                    "properties": {
                        "expression": {"type": "string", "description": "Mathematical expression to evaluate"}
                    },
                    "required": ["expression"]
                }
            },
            {
                "name": "text_processor", 
                "description": "Processes text with various operations",
                "execute_func": "example_usage.my_text_processor",
                "schema": {
                    "type": "object",
                    "properties": {
                        "text": {"type": "string", "description": "Text to process"},
                        "operation": {"type": "string", "description": "Operation: uppercase, lowercase, reverse"}
                    },
                    "required": ["text"]
                }
            },
            {
                "name": "data_formatter",
                "description": "Formats data in different formats", 
                "execute_func": "example_usage.my_data_formatter",
                "schema": {
                    "type": "object",
                    "properties": {
                        "data": {"type": "string", "description": "Data to format"},
                        "format": {"type": "string", "description": "Format type: json, csv"}
                    },
                    "required": ["data"]
                }
            }
        ]
    }
    
    # Set up environment (user provides their own API key)
    if not os.getenv("OPENAI_API_KEY"):
        print("Note: Set OPENAI_API_KEY environment variable for full functionality")
        print("Running in demo mode with mock responses...\n")
    
    print("🚀 AI Utility Orchestrator - Dynamic Usage Example")
    print("=" * 50)
    
    # Example queries that will be routed to appropriate tools
    test_queries = [
        "Calculate 15 * 8 + 32",
        "Convert this text to uppercase: hello world",
        "Format this JSON: {'name': 'John', 'age': 30}",
        "What's the reverse of 'dynamic'?",
        "What tools are available?"
    ]
    
    for i, query in enumerate(test_queries, 1):
        print(f"\n{i}. Query: {query}")
        print("-" * 40)
        
        try:
            # Execute with user's dynamic configuration
            result = agent_executor(
                user_input=query,
                config=user_config,
                user_id="demo_user"
            )
            
            print(f"Selected Tool: {result.get('selected_tool', 'none')}")
            print(f"Response: {result.get('final_response', 'No response')}")
            
            if result.get('tool_result'):
                print(f"Tool Result: {result['tool_result']}")
                
        except Exception as e:
            print(f"Error: {str(e)}")
            print("This is expected if no API key is provided or if there are configuration issues.")

def run_minimal_example():
    """Show the absolute minimum required to use the system."""
    print("\n" + "=" * 50)
    print("🔥 MINIMAL EXAMPLE - Absolute minimum required")
    print("=" * 50)
    
    # Absolute minimum configuration
    minimal_config = {
        "llm": {
            "model": "gpt-4o-mini"  # Only model is required
        },
        "system_prompt": "You are a helpful assistant.",
        "tools": []  # Even tools are optional
    }
    
    try:
        result = agent_executor(
            user_input="Hello, what can you help me with?",
            config=minimal_config
        )
        print(f"Response: {result.get('final_response', 'No response')}")
    except Exception as e:
        print(f"Minimal example result: {str(e)}")
        print("This shows the system requires proper configuration - no hardcoded fallbacks!")

if __name__ == "__main__":
    print("AI Utility Orchestrator - Completely Dynamic System")
    print("No hardcoding, no default configurations, fully user-controlled!")
    print("\nThis example shows how to use the system in any project by providing:")
    print("- Your own model configuration")
    print("- Your own system prompt") 
    print("- Your own tools")
    print("- Your own imports")
    
    run_dynamic_example()
    run_minimal_example()
    
    print("\n" + "=" * 50)
    print("✅ Dynamic system validation complete!")
    print("The system works with ZERO hardcoded values.")
    print("Everything is provided by the user dynamically.")
